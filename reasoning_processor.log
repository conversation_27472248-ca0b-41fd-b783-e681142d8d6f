2025-07-15 14:07:19,099 - __main__ - INFO - Ensured directory exists: data/processed
2025-07-15 14:07:19,099 - __main__ - INFO - Ensured directory exists: data/processed/reasoning
2025-07-15 14:07:19,115 - __main__ - INFO - Ensured directory exists: reports/quality
2025-07-15 14:07:19,115 - __main__ - INFO - Configuration loaded successfully
2025-07-15 14:07:19,115 - __main__ - INFO - Initializing enhanced reasoning orchestrator...
2025-07-15 14:07:19,115 - __main__ - INFO - Found 2 files to process
2025-07-15 14:07:19,115 - __main__ - INFO - Processing file: features_Bank_Nifty_5.csv
2025-07-15 14:07:19,115 - src.reasoning_system.core.enhanced_orchestrator - INFO - Starting processing for data\processed\features_Bank_Nifty_5.csv
2025-07-15 14:07:19,177 - src.reasoning_system.core.enhanced_orchestrator - INFO - Loaded 800 rows from data\processed\features_Bank_Nifty_5.csv
2025-07-15 14:07:19,177 - src.reasoning_system.core.enhanced_orchestrator - ERROR - Error processing data\processed\features_Bank_Nifty_5.csv: 'processing'
Traceback (most recent call last):
  File "C:\AlgoTrading\src\reasoning_system\core\enhanced_orchestrator.py", line 55, in process_file
    if (i + 1) % self.config['processing']['progress_reporting_interval'] == 0:
                 ~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'processing'
2025-07-15 14:07:19,177 - __main__ - ERROR - Failed to process features_Bank_Nifty_5.csv: 'processing'
2025-07-15 14:07:19,177 - __main__ - INFO - Processing file: features_Nifty_2.csv
2025-07-15 14:07:19,177 - src.reasoning_system.core.enhanced_orchestrator - INFO - Starting processing for data\processed\features_Nifty_2.csv
2025-07-15 14:07:19,240 - src.reasoning_system.core.enhanced_orchestrator - INFO - Loaded 2300 rows from data\processed\features_Nifty_2.csv
2025-07-15 14:07:19,240 - src.reasoning_system.core.enhanced_orchestrator - ERROR - Error processing data\processed\features_Nifty_2.csv: 'processing'
Traceback (most recent call last):
  File "C:\AlgoTrading\src\reasoning_system\core\enhanced_orchestrator.py", line 55, in process_file
    if (i + 1) % self.config['processing']['progress_reporting_interval'] == 0:
                 ~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'processing'
2025-07-15 14:07:19,240 - __main__ - ERROR - Failed to process features_Nifty_2.csv: 'processing'
2025-07-15 14:07:38,920 - __main__ - INFO - Ensured directory exists: data/processed
2025-07-15 14:07:38,920 - __main__ - INFO - Ensured directory exists: data/processed/reasoning
2025-07-15 14:07:38,920 - __main__ - INFO - Ensured directory exists: reports/quality
2025-07-15 14:07:38,920 - __main__ - INFO - Configuration loaded successfully
2025-07-15 14:07:38,920 - __main__ - INFO - Initializing enhanced reasoning orchestrator...
2025-07-15 14:07:38,920 - __main__ - INFO - Found 2 files to process
2025-07-15 14:07:38,920 - __main__ - INFO - Processing file: features_Bank_Nifty_5.csv
2025-07-15 14:07:38,920 - src.reasoning_system.core.enhanced_orchestrator - INFO - Starting processing for data\processed\features_Bank_Nifty_5.csv
2025-07-15 14:07:38,951 - src.reasoning_system.core.enhanced_orchestrator - INFO - Loaded 800 rows from data\processed\features_Bank_Nifty_5.csv
2025-07-15 14:07:38,961 - src.reasoning_system.core.enhanced_orchestrator - ERROR - Error processing data\processed\features_Bank_Nifty_5.csv: 'processing'
Traceback (most recent call last):
  File "C:\AlgoTrading\src\reasoning_system\core\enhanced_orchestrator.py", line 55, in process_file
    if (i + 1) % self.config['processing']['progress_reporting_interval'] == 0:
                 ~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'processing'
2025-07-15 14:07:38,961 - __main__ - ERROR - Failed to process features_Bank_Nifty_5.csv: 'processing'
2025-07-15 14:07:38,961 - __main__ - INFO - Processing file: features_Nifty_2.csv
2025-07-15 14:07:38,961 - src.reasoning_system.core.enhanced_orchestrator - INFO - Starting processing for data\processed\features_Nifty_2.csv
2025-07-15 14:07:38,998 - src.reasoning_system.core.enhanced_orchestrator - INFO - Loaded 2300 rows from data\processed\features_Nifty_2.csv
2025-07-15 14:07:38,998 - src.reasoning_system.core.enhanced_orchestrator - ERROR - Error processing data\processed\features_Nifty_2.csv: 'processing'
Traceback (most recent call last):
  File "C:\AlgoTrading\src\reasoning_system\core\enhanced_orchestrator.py", line 55, in process_file
    if (i + 1) % self.config['processing']['progress_reporting_interval'] == 0:
                 ~~~~~~~~~~~^^^^^^^^^^^^^^
KeyError: 'processing'
2025-07-15 14:07:38,998 - __main__ - ERROR - Failed to process features_Nifty_2.csv: 'processing'
2025-07-15 14:08:00,913 - __main__ - INFO - Ensured directory exists: data/processed
2025-07-15 14:08:00,913 - __main__ - INFO - Ensured directory exists: data/processed/reasoning
2025-07-15 14:08:00,913 - __main__ - INFO - Ensured directory exists: reports/quality
2025-07-15 14:08:00,913 - __main__ - INFO - Configuration loaded successfully
2025-07-15 14:08:00,913 - __main__ - INFO - Initializing enhanced reasoning orchestrator...
2025-07-15 14:08:00,929 - __main__ - INFO - Found 2 files to process
2025-07-15 14:08:00,929 - __main__ - INFO - Processing file: features_Bank_Nifty_5.csv
2025-07-15 14:08:00,929 - src.reasoning_system.core.enhanced_orchestrator - INFO - Starting processing for data\processed\features_Bank_Nifty_5.csv
2025-07-15 14:08:00,944 - src.reasoning_system.core.enhanced_orchestrator - INFO - Loaded 800 rows from data\processed\features_Bank_Nifty_5.csv
2025-07-15 14:08:01,635 - src.reasoning_system.core.enhanced_orchestrator - INFO - Successfully processed and saved to data\processed\reasoning\reasoning_Bank_Nifty_5.csv
2025-07-15 14:08:01,697 - __main__ - INFO - Successfully processed features_Bank_Nifty_5.csv
2025-07-15 14:08:01,697 - __main__ - INFO -   - Input rows: 800
2025-07-15 14:08:01,697 - __main__ - INFO -   - Output rows: 800
2025-07-15 14:08:01,697 - __main__ - INFO -   - Reasoning columns added: True
2025-07-15 14:08:01,697 - __main__ - INFO -   - Quality score: 66.2
2025-07-15 14:08:01,697 - __main__ - INFO -   - Output file: reasoning_Bank_Nifty_5.csv
2025-07-15 14:08:01,697 - __main__ - INFO - Processing file: features_Nifty_2.csv
2025-07-15 14:08:01,697 - src.reasoning_system.core.enhanced_orchestrator - INFO - Starting processing for data\processed\features_Nifty_2.csv
2025-07-15 14:08:01,775 - src.reasoning_system.core.enhanced_orchestrator - INFO - Loaded 2300 rows from data\processed\features_Nifty_2.csv
2025-07-15 14:08:02,496 - src.reasoning_system.core.enhanced_orchestrator - INFO - Processed 1000/2300 rows for features_Nifty_2.csv
2025-07-15 14:08:03,191 - src.reasoning_system.core.enhanced_orchestrator - INFO - Processed 2000/2300 rows for features_Nifty_2.csv
2025-07-15 14:08:03,718 - src.reasoning_system.core.enhanced_orchestrator - INFO - Successfully processed and saved to data\processed\reasoning\reasoning_Nifty_2.csv
2025-07-15 14:08:03,953 - __main__ - INFO - Successfully processed features_Nifty_2.csv
2025-07-15 14:08:03,953 - __main__ - INFO -   - Input rows: 2300
2025-07-15 14:08:03,953 - __main__ - INFO -   - Output rows: 2300
2025-07-15 14:08:03,953 - __main__ - INFO -   - Reasoning columns added: True
2025-07-15 14:08:03,953 - __main__ - INFO -   - Quality score: 69.8
2025-07-15 14:08:03,953 - __main__ - INFO -   - Output file: reasoning_Nifty_2.csv
2025-07-15 14:08:03,953 - __main__ - INFO - Summary report saved to: reports\quality\processing_summary.txt
2025-07-15 14:10:40,170 - __main__ - INFO - Ensured directory exists: data/processed
2025-07-15 14:10:40,170 - __main__ - INFO - Ensured directory exists: data/processed/reasoning
2025-07-15 14:10:40,170 - __main__ - INFO - Ensured directory exists: reports/quality
2025-07-15 14:10:40,170 - __main__ - INFO - Configuration loaded successfully
2025-07-15 14:10:40,170 - __main__ - INFO - Initializing enhanced reasoning orchestrator...
2025-07-15 14:10:40,170 - __main__ - INFO - Found 2 files to process
2025-07-15 14:10:40,170 - __main__ - INFO - Processing file: features_Bank_Nifty_5.csv
2025-07-15 14:10:40,170 - src.reasoning_system.core.enhanced_orchestrator - INFO - Starting processing for data\processed\features_Bank_Nifty_5.csv
2025-07-15 14:10:40,201 - src.reasoning_system.core.enhanced_orchestrator - INFO - Loaded 800 rows from data\processed\features_Bank_Nifty_5.csv
2025-07-15 14:10:40,201 - src.reasoning_system.core.enhanced_orchestrator - ERROR - Error processing data\processed\features_Bank_Nifty_5.csv: 'SMA_50'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\LocalCache\local-packages\Python312\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "pandas/_libs/index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7096, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'SMA_50'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\AlgoTrading\src\reasoning_system\core\enhanced_orchestrator.py", line 49, in process_file
    reasoning, decision = self._generate_reasoning_for_row(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AlgoTrading\src\reasoning_system\core\enhanced_orchestrator.py", line 87, in _generate_reasoning_for_row
    historical_patterns = self.pattern_engine.identify_patterns(historical_data, current_row_features)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AlgoTrading\src\reasoning_system\engines\historical_pattern_engine.py", line 57, in identify_patterns
    prev_sma_50 = historical_data['SMA_50'].iloc[-1] if len(historical_data) > 0 else None
                  ~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\LocalCache\local-packages\Python312\site-packages\pandas\core\frame.py", line 4107, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\LocalCache\local-packages\Python312\site-packages\pandas\core\indexes\base.py", line 3819, in get_loc
    raise KeyError(key) from err
KeyError: 'SMA_50'
2025-07-15 14:10:40,217 - __main__ - ERROR - Failed to process features_Bank_Nifty_5.csv: 'SMA_50'
2025-07-15 14:10:40,217 - __main__ - INFO - Processing file: features_Nifty_2.csv
2025-07-15 14:10:40,217 - src.reasoning_system.core.enhanced_orchestrator - INFO - Starting processing for data\processed\features_Nifty_2.csv
2025-07-15 14:10:40,280 - src.reasoning_system.core.enhanced_orchestrator - INFO - Loaded 2300 rows from data\processed\features_Nifty_2.csv
2025-07-15 14:10:40,280 - src.reasoning_system.core.enhanced_orchestrator - ERROR - Error processing data\processed\features_Nifty_2.csv: 'SMA_50'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\LocalCache\local-packages\Python312\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "pandas/_libs/index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas/_libs/hashtable_class_helper.pxi", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas/_libs/hashtable_class_helper.pxi", line 7096, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'SMA_50'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\AlgoTrading\src\reasoning_system\core\enhanced_orchestrator.py", line 49, in process_file
    reasoning, decision = self._generate_reasoning_for_row(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AlgoTrading\src\reasoning_system\core\enhanced_orchestrator.py", line 87, in _generate_reasoning_for_row
    historical_patterns = self.pattern_engine.identify_patterns(historical_data, current_row_features)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\AlgoTrading\src\reasoning_system\engines\historical_pattern_engine.py", line 57, in identify_patterns
    prev_sma_50 = historical_data['SMA_50'].iloc[-1] if len(historical_data) > 0 else None
                  ~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\LocalCache\local-packages\Python312\site-packages\pandas\core\frame.py", line 4107, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.12_qbz5n2kfra8p0\LocalCache\local-packages\Python312\site-packages\pandas\core\indexes\base.py", line 3819, in get_loc
    raise KeyError(key) from err
KeyError: 'SMA_50'
2025-07-15 14:10:40,280 - __main__ - ERROR - Failed to process features_Nifty_2.csv: 'SMA_50'
2025-07-15 14:11:30,302 - __main__ - INFO - Ensured directory exists: data/processed
2025-07-15 14:11:30,302 - __main__ - INFO - Ensured directory exists: data/processed/reasoning
2025-07-15 14:11:30,302 - __main__ - INFO - Ensured directory exists: reports/quality
2025-07-15 14:11:30,302 - __main__ - INFO - Configuration loaded successfully
2025-07-15 14:11:30,302 - __main__ - INFO - Initializing enhanced reasoning orchestrator...
2025-07-15 14:11:30,302 - __main__ - INFO - Found 2 files to process
2025-07-15 14:11:30,302 - __main__ - INFO - Processing file: features_Bank_Nifty_5.csv
2025-07-15 14:11:30,302 - src.reasoning_system.core.enhanced_orchestrator - INFO - Starting processing for data\processed\features_Bank_Nifty_5.csv
2025-07-15 14:11:30,333 - src.reasoning_system.core.enhanced_orchestrator - INFO - Loaded 800 rows from data\processed\features_Bank_Nifty_5.csv
2025-07-15 14:11:32,118 - src.reasoning_system.core.enhanced_orchestrator - INFO - Successfully processed and saved to data\processed\reasoning\reasoning_Bank_Nifty_5.csv
2025-07-15 14:11:32,212 - __main__ - INFO - Successfully processed features_Bank_Nifty_5.csv
2025-07-15 14:11:32,212 - __main__ - INFO -   - Input rows: 800
2025-07-15 14:11:32,212 - __main__ - INFO -   - Output rows: 800
2025-07-15 14:11:32,212 - __main__ - INFO -   - Reasoning columns added: True
2025-07-15 14:11:32,212 - __main__ - INFO -   - Quality score: 65.8
2025-07-15 14:11:32,228 - __main__ - INFO -   - Output file: reasoning_Bank_Nifty_5.csv
2025-07-15 14:11:32,228 - __main__ - INFO - Processing file: features_Nifty_2.csv
2025-07-15 14:11:32,228 - src.reasoning_system.core.enhanced_orchestrator - INFO - Starting processing for data\processed\features_Nifty_2.csv
2025-07-15 14:11:32,274 - src.reasoning_system.core.enhanced_orchestrator - INFO - Loaded 2300 rows from data\processed\features_Nifty_2.csv
2025-07-15 14:11:34,356 - src.reasoning_system.core.enhanced_orchestrator - INFO - Processed 1000/2300 rows for features_Nifty_2.csv
2025-07-15 14:11:36,529 - src.reasoning_system.core.enhanced_orchestrator - INFO - Processed 2000/2300 rows for features_Nifty_2.csv
2025-07-15 14:11:37,406 - src.reasoning_system.core.enhanced_orchestrator - INFO - Successfully processed and saved to data\processed\reasoning\reasoning_Nifty_2.csv
2025-07-15 14:11:37,625 - __main__ - INFO - Successfully processed features_Nifty_2.csv
2025-07-15 14:11:37,625 - __main__ - INFO -   - Input rows: 2300
2025-07-15 14:11:37,625 - __main__ - INFO -   - Output rows: 2300
2025-07-15 14:11:37,625 - __main__ - INFO -   - Reasoning columns added: True
2025-07-15 14:11:37,625 - __main__ - INFO -   - Quality score: 69.9
2025-07-15 14:11:37,625 - __main__ - INFO -   - Output file: reasoning_Nifty_2.csv
2025-07-15 14:11:37,625 - __main__ - INFO - Summary report saved to: reports\quality\processing_summary.txt
